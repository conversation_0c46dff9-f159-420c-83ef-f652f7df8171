# 母设备低功耗休眠唤醒功能说明

## 功能概述
本代码实现了母设备的低功耗休眠唤醒功能：
- **INT1** (PB5): 光敏1触发 - 中断唤醒
- **INT2** (PA8): 触摸传感器 - 中断唤醒
- **E70通信**: 主动唤醒子设备并获取数据
- **低功耗休眠**: STOP模式，极低功耗待机

## 硬件连接
```
INT1 → STM32 PB5 (上升沿中断)
INT2 → STM32 PA8 (上升沿中断)
EN   → STM32 PB4 (普通输入)
LED  → STM32 PC14 (状态指示)
```

## 业务流程说明

### 1. 唤醒阶段 (MASTER_STATE_WAKE_UP)
- **触发方式**: INT1 (PB5) 或 INT2 (PA8) 中断唤醒
- **功能**: 从STOP模式唤醒，恢复系统时钟和外设
- **输出**: "Master Device Wake Up"

### 2. 电压检测阶段 (MASTER_STATE_VOLTAGE_CHECK)
- **功能**: 等待0.5秒电源稳定后检测电池电压
- **输出**: "Master Battery Voltage: X.XXXV (XXXXmV) - Saved"

### 3. E70唤醒子设备阶段 (MASTER_STATE_E70_WAKE_SLAVE)
- **功能**: 开启E70电源，发送唤醒信号给子设备
- **发送数据**: FC FD + 55 66 + FD
- **输出**: "Sending wake signal to slave device..."

### 4. 等待子设备数据阶段 (MASTER_STATE_WAIT_SLAVE_DATA)
- **功能**: 等待子设备回复数据，0.5秒间隔重试，最多6次
- **期待数据**: FA FB + ID(2字节) + 电压(2字节) + FD (大端序)
- **重要机制**: **只接收第一个子设备的数据**，收到后立即停止接收
- **多设备处理**: 忽略其他子设备的数据，避免冲突
- **超时处理**: 6次无回复后进入休眠

### 5. 发送确认阶段 (MASTER_STATE_SEND_ACK)
- **功能**: 收到第一个子设备数据后发送确认信号
- **发送数据**: FC FD + 77 88 + FD
- **接收保护**: 确保接收已停止，不再接收其他子设备数据
- **输出**: 显示通信摘要（母设备电压、第一个子设备ID、第一个子设备电压）

### 6. 准备休眠阶段 (MASTER_STATE_PREPARE_SLEEP)
- **功能**: 关闭所有外设，配置低功耗模式，进入STOP休眠
- **唤醒条件**: INT1或INT2中断

## 通信协议

### 母设备发送的数据包：
1. **唤醒信号**: `FC FD 55 66 FD` (5字节)
2. **确认信号**: `FC FD 77 88 FD` (5字节)

### 子设备发送的数据包：
- **设备数据**: `FA FB + ID(2字节) + 电压(2字节) + FD` (7字节，大端序)

## 多子设备兼容机制
- **第一优先原则**: 每次唤醒周期只接收第一个回复的子设备数据
- **立即停止接收**: 收到第一个有效数据包后立即停止UART接收
- **避免冲突**: 忽略其他子设备的后续数据，防止数据混乱
- **周期重置**: 每次新的唤醒周期重新开始接收第一个数据

## 低功耗特性
- **休眠模式**: STM32L071 STOP模式
- **功耗**: 极低功耗（微安级别）
- **唤醒源**: INT1 (PB5) 或 INT2 (PA8) 外部中断
- **外设管理**: 休眠时关闭所有非必要外设和时钟

## 串口输出示例
```
=== STM32L071 Master Device Low Power System ===
=== E70 First Time Initialization ===
=== E70 Configuration Complete ===
Master Device Ready

=== Master Device Wake Up ===
=== Voltage Check Phase ===
Master Battery Voltage: 3.245V (3245mV) - Saved
=== E70 Wake Slave Phase ===
Powering ON E70 module...
E70 Ready for Communication
Sending wake signal to slave device...
First Slave Data Received: ID=0x1234, Voltage=3300mV
Stopping reception to ignore other slave devices
=== Send ACK Phase ===
Sending ACK to first slave device...
Communication completed successfully!
Data Summary (First Device Only):
- Master Battery: 3245mV
- First Slave ID: 0x1234
- First Slave Battery: 3300mV
- Other slave devices ignored in this wake cycle
=== Prepare Sleep Phase ===
Entering sleep mode...
Preparing to enter STOP mode...
Device will wake up on INT1 (PB5) or INT2 (PA8) interrupt

*** MASTER WAKE UP from STOP mode ***
```

## 测试方法
1. 连接串口调试工具 (115200bps)
2. 上电后查看初始化信息，设备会自动进入休眠
3. 触发INT1或INT2信号唤醒设备
4. 观察完整的通信流程
5. 验证设备重新进入休眠

## 注意事项
- 首次上电会进行E70初始化，之后每次唤醒直接使用
- 休眠时所有外设时钟关闭，功耗极低
- 唤醒后会自动恢复系统时钟和外设配置
- 通信失败或超时会自动重试，最多6次后进入休眠
- **重要**: 每次唤醒周期只处理第一个子设备的数据，其他子设备数据会被忽略
- 如果有多个子设备，建议在子设备端增加随机延时避免同时发送
