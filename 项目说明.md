本项目是低功耗项目。
本项目使用STM32L071KBU6单片机，外设有E70 LoRa模块，电池电压检测电路、4G CAT1模块、2个光敏触发输入、1个触摸输入。E70 LoRa模块用于和近场子设备通信，CAT1模块用于和远端服务器通信，电池电压检测电路用于检测电池电压。光敏传感器和触摸传感器用于唤醒设备。

当前代码中，MLX90393模块代码、E70模块代码是从STM32L031G6U6子设备端运行的代码拷贝过来的，检查代码是否需要修改。

硬件连接：
### **E70模式管理**

#### **工作模式定义**
```
M2 M1 M0 | 模式
---------|----------
0  <USER>  <GROUP>  | 禁用模式 (禁用所有通信) ❌
0  0  1  | 连续通信模式 (正常通信) ✅
0  1  0  | 省电模式
0  1  1  | 配置模式3 (实测不工作) ❌
1  0  0  | RSSI模式
1  0  1  | 配置模式5 (实测工作) ✅
1  1  0  | 保留模式
1  1  1  | 深度休眠模式
```

#### **关键原则**
1. **E70模式在上电时锁存** - 必须先设置引脚，后上电
2. **首次初始化**: 配置模式(101) → 设置参数 → 通信模式(001)
3. **每次唤醒**: 直接设置为通信模式(001)，无需重复配置
4. **模式切换**: 必须使用E70_SetModeWithPowerCycle()，不能运行时切换

#### **MLX90393低功耗配置**
- **增益**: 使用最低增益设置
- **分辨率**: 设置为最低分辨率
- **过采样**: 最低过采样率
- **WOC模式**: 间歇采样，1秒间隔

### **硬件连接**
1. **触摸传感器 INT2** → **STM32 PA8** (上升沿中断)
2. **E70 M0** → **STM32 PA4** (模式控制)
3. **E70 M1** → **STM32 PA5** (模式控制)
4. **E70 M2** → **STM32 PA6** (模式控制)
5. **E70 VCC** → **STM32 RF_PWR_pin** (电源控制)
6. **电池电压** → **STM32 PB0** (ADC采样，2:1分压)
7. **光敏1触发** → **STM32 PB5** (唤醒设备)
8. **光敏2检测** → **STM32 PB4** (光敏1触发设备 光敏2多角度双重检测)
9. **E70通信** → **STM32 LPUART1** (通信接口)
10. **E70电源开关** → **STM32 RF_PWR_pin** (电源开关)
11. **CAT1通信** → **STM32 USART1** (通信接口)
12. **CAT1电源开关** → **STM32 CAT1_PWR_Pin** (电源开关)
13. LED → **STM32 PC14** (指示灯 在gpio.h中定义)



### **软件开发注意事项**

#### **编译和调试**
- **Keil项目**: 所有STM32工程都是Keil项目，不要尝试编译
- **调试输出**: 使用UART2 (115200bps) 进行调试
- **中断安全**: 中断回调中不使用printf或长时间操作

#### **参数调整**
- **MLX90393阈值**: 在mlx90393.h中调整WOC_XY_THRESHOLD和WOC_Z_THRESHOLD
- **E70设备ID**: 在e70_config.h中修改MCU_DEVICE_ID
- **电池校准**: 在e70_config.h中调整BATTERY_VOLTAGE_CALIBRATION_FACTOR

